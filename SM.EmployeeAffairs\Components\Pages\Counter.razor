﻿@page "/counter"
@rendermode InteractiveServer
@inject ISnackbar Snackbar

<PageTitle>Counter</PageTitle>

<MudText Typo="Typo.h3" GutterBottom="true">Counter</MudText>
<AuthorizeView>
    <Authorized>
        <MudText Typo="Typo.body1" Class="mb-4">Current count: @currentCount</MudText>
    </Authorized>
</AuthorizeView>

<AuthorizeView Roles="Admin">
    <MudButton Color="Color.Primary" Variant="Variant.Filled" @onclick="IncrementCount">زر خاص بالمدير (Admin)</MudButton>
</AuthorizeView>

<AuthorizeView Roles="User">
    <MudButton Color="Color.Secondary" Variant="Variant.Filled" @onclick="ShowUserMessage">زر خاص بالمستخدم (User)</MudButton>
</AuthorizeView>

@code {
    private int currentCount = 0;

    private void IncrementCount()
    {
        currentCount++;
    }

    private void ShowUserMessage()
    {
        Snackbar.Add("مرحباً أيها المستخدم!", Severity.Normal);
    }
}
