@page "/order"
@using OnlineStore.Data.Entities
@using OnlineStore.Data
@using Microsoft.EntityFrameworkCore
@using OrderEntity = OnlineStore.Data.Entities.Order
@inject ApplicationDbContext DbContext
@inject IJSRuntime JSRuntime
@rendermode InteractiveServer

<PageTitle>Order</PageTitle>

<MudContainer MaxWidth="MaxWidth.Large" Class="mt-4">
    <MudText Typo="Typo.h4" Class="mb-4">Order Management</MudText>

    @if (!isCustomerLoggedIn)
    {
        <!-- Customer Login Section -->
        <MudCard Class="mb-4">
            <MudCardContent>
                <MudText Typo="Typo.h5" Class="mb-3">Customer Login</MudText>
                
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="customerEmail" 
                                      Label="Email" 
                                      Variant="Variant.Outlined" 
                                      Required="true" />
                    </MudItem>
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="customerName" 
                                      Label="Name" 
                                      Variant="Variant.Outlined" 
                                      Required="true" />
                    </MudItem>
                </MudGrid>

                <MudButton Variant="Variant.Filled" 
                           Color="Color.Primary" 
                           Class="mt-3"
                           OnClick="LoginCustomer">
                    Login / Register
                </MudButton>
            </MudCardContent>
        </MudCard>
    }
    else
    {
        <!-- Order Creation Section -->
        <MudCard Class="mb-4">
            <MudCardContent>
                <MudText Typo="Typo.h5" Class="mb-3">Welcome, @currentCustomer?.Name!</MudText>
                
                <MudGrid>
                    <MudItem xs="12" md="6">
                        <MudTextField @bind-Value="orderTotal" 
                                      Label="Order Total" 
                                      Variant="Variant.Outlined" 
                                      Required="true"
                                      Format="F2" />
                    </MudItem>
                    <MudItem xs="12" md="6">
                        <MudSelect @bind-Value="orderStatus" 
                                   Label="Status" 
                                   Variant="Variant.Outlined">
                            <MudSelectItem Value="@("Pending")">Pending</MudSelectItem>
                            <MudSelectItem Value="@("Processing")">Processing</MudSelectItem>
                            <MudSelectItem Value="@("Completed")">Completed</MudSelectItem>
                        </MudSelect>
                    </MudItem>
                </MudGrid>

                <MudButton Variant="Variant.Filled" 
                           Color="Color.Success" 
                           Class="mt-3 mr-2"
                           OnClick="CreateOrder">
                    Create Order
                </MudButton>

                <MudButton Variant="Variant.Outlined" 
                           Color="Color.Secondary" 
                           Class="mt-3"
                           OnClick="Logout">
                    Logout
                </MudButton>
            </MudCardContent>
        </MudCard>

        <!-- Orders List -->
        <MudCard>
            <MudCardContent>
                <MudText Typo="Typo.h5" Class="mb-3">Your Orders</MudText>
                
                <MudTable Items="@orders" Hover="true" Striped="true">
                    <HeaderContent>
                        <MudTh>Order ID</MudTh>
                        <MudTh>Date</MudTh>
                        <MudTh>Total</MudTh>
                        <MudTh>Status</MudTh>
                        <MudTh>Actions</MudTh>
                    </HeaderContent>
                    <RowTemplate>
                        <MudTd DataLabel="Order ID">@context.Id</MudTd>
                        <MudTd DataLabel="Date">@context.Date.ToString("dd/MM/yyyy")</MudTd>
                        <MudTd DataLabel="Total">@context.Total.ToString("C")</MudTd>
                        <MudTd DataLabel="Status">@context.Status</MudTd>
                        <MudTd DataLabel="Actions">
                            @if (context.Status != "Cancelled")
                            {
                                <MudButton Size="Size.Small" 
                                           Variant="Variant.Filled" 
                                           Color="Color.Error"
                                           OnClick="() => CancelOrder(context)">
                                    Cancel
                                </MudButton>
                            }
                        </MudTd>
                    </RowTemplate>
                </MudTable>
            </MudCardContent>
        </MudCard>
    }
</MudContainer>

@code {
    private bool isCustomerLoggedIn = false;
    private Customer? currentCustomer;
    private List<OrderEntity> orders = new();
    
    // Login form fields
    private string customerEmail = string.Empty;
    private string customerName = string.Empty;
    
    // Order form fields
    private float orderTotal = 0;
    private string orderStatus = "Pending";

    private async Task LoginCustomer()
    {
        if (string.IsNullOrWhiteSpace(customerEmail) || string.IsNullOrWhiteSpace(customerName))
        {
            await JSRuntime.InvokeVoidAsync("alert", "Please enter both email and name.");
            return;
        }

        // Check if customer exists
        currentCustomer = await DbContext.Customers
            .Include(c => c.Orders)
            .FirstOrDefaultAsync(c => c.Email == customerEmail);

        if (currentCustomer == null)
        {
            // Create new customer
            currentCustomer = new Customer
            {
                Name = customerName,
                Email = customerEmail
            };

            DbContext.Customers.Add(currentCustomer);
            await DbContext.SaveChangesAsync();
        }

        isCustomerLoggedIn = true;
        orders = currentCustomer.Orders;
        StateHasChanged();
    }

    private async Task CreateOrder()
    {
        if (currentCustomer == null || orderTotal <= 0)
        {
            await JSRuntime.InvokeVoidAsync("alert", "Please enter a valid order total.");
            return;
        }

        var newOrder = new OrderEntity
        {
            Date = DateTime.Now,
            Total = orderTotal,
            Status = orderStatus,
            CustomerId = currentCustomer.Id
        };

        DbContext.Orders.Add(newOrder);
        await DbContext.SaveChangesAsync();

        // Refresh orders list
        orders = await DbContext.Orders
            .Where(o => o.CustomerId == currentCustomer.Id)
            .ToListAsync();

        // Reset form
        orderTotal = 0;
        orderStatus = "Pending";

        await JSRuntime.InvokeVoidAsync("alert", "Order created successfully!");
        StateHasChanged();
    }

    private async Task CancelOrder(OrderEntity order)
    {
        order.CancelOrder();
        DbContext.Orders.Update(order);
        await DbContext.SaveChangesAsync();

        await JSRuntime.InvokeVoidAsync("alert", "Order cancelled successfully!");
        StateHasChanged();
    }

    private void Logout()
    {
        isCustomerLoggedIn = false;
        currentCustomer = null;
        orders.Clear();
        customerEmail = string.Empty;
        customerName = string.Empty;
        orderTotal = 0;
        orderStatus = "Pending";
        StateHasChanged();
    }
}
