﻿@page "/AdministrationEdit"
@page "/AdministrationEdit/{Id}"
@inject HttpClient Http
@inject ISnackbar Snackbar
@inject NavigationManager NavigationManager
@rendermode InteractiveServer

@using SM.EmployeeAffairs.Components.Pages.EmployeesComponents
@using SM.EmployeeAffairs.Data.Entities

@if (Administration == null)
{
    <div class="d-flex justify-center align-center ma-auto">
        <MudProgressCircular Color="Color.Primary" Size="Size.Large" Indeterminate="true" />
    </div>
}
else
{
    <MudForm Model="Administration" @ref="form">
        <MudCard Elevation="6">
            <MudCardHeader>
                <MudText Typo="Typo.h5" Align="Align.Center">@PageHeaderText</MudText>
            </MudCardHeader>
            <MudItem xs="12" md="12" lg="12">
                <MudTextField @bind-Value="Administration.Name"
                              Label="الإسم"
                              Variant="Variant.Outlined"
                              Margin="Margin.Dense" />
            </MudItem>
            <MudItem xs="12" md="12" lg="12">
                <MudTextField @bind-Value="Administration.Description"
                              Label="الوصف"
                              Variant="Variant.Outlined"
                              Margin="Margin.Dense" />
            </MudItem>
            <MudCardActions Class="d-flex justify-end mt-2">
                <MudButton Variant="Variant.Outlined"
                           Color="Color.Primary"
                           OnClick="HandleSaveAsync">حفظ</MudButton>
                <MudButton Variant="Variant.Filled"
                           Color="Color.Primary"
                           OnClick="HandleSaveAndCloseAsync">حفظ و إغلاق</MudButton>
            </MudCardActions>
        </MudCard>

    </MudForm>
    <MudSpacer />
    <MudCard Elevation="6">
        <CascadingValue Value="Administration.Id" Name="AdministrationId">
            <MudCardHeader>
                <MudText Typo="Typo.h5" Align="Align.Center">الموظفين في الإدارة</MudText>
            </MudCardHeader>
            <EmployeesListComponent />
        </CascadingValue>
    </MudCard>
}

@code {
    protected MudForm? form;
    protected bool success = false;
    private string PageHeaderText = "إضافة إدارة جديدة";

    [Parameter] public string Id { get; set; } = string.Empty;

    private Administration? Administration { get; set; } 

    protected override async Task OnInitializedAsync()
    {
        if (string.IsNullOrEmpty(Id))
        {
            PageHeaderText = "إضافة إدارة جديدة";
            Administration = new Administration();
        }
        else
        {
            PageHeaderText = "تعديل إدارة";
            Administration = await Http.GetFromJsonAsync<Administration>($"/api/administrations/{Guid.Parse(Id)}");
        }
    }

    private async Task HandleSaveAsync()
    {
        if (Administration?.Id == Guid.Empty)
        {
            var response = await Http.PostAsJsonAsync("/api/administrations", Administration);
            if (response.IsSuccessStatusCode)
            {
                Snackbar.Configuration.PositionClass = Defaults.Classes.Position.TopCenter;
                Snackbar.Add("تم الحفظ بنجاح", Severity.Success);
            }
        }
        else
        {
            var response = await Http.PutAsJsonAsync($"/api/administrations/{Administration.Id}", Administration);
            if (response.IsSuccessStatusCode)
            {
                
                Snackbar.Configuration.PositionClass = Defaults.Classes.Position.TopCenter;
                Snackbar.Add("تم الحفظ بنجاح", Severity.Success);
            }
        }
    }

    private async Task HandleSaveAndCloseAsync()
    {
        await HandleSaveAsync();
        NavigationManager.NavigateTo("/AdministrationList", true);
    }
}
