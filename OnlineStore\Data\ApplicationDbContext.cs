using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using OnlineStore.Data.Entities;

namespace OnlineStore.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : base(options)
        {
        }

        public DbSet<Administration> Administrations { get; set; } = default!;
        public DbSet<Employee> Employees { get; set; } = default!;
        public DbSet<Customer> Customers { get; set; } = default!;
        public DbSet<Order> Orders { get; set; } = default!;

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<Administration>(entity =>
            {
                entity.ToTable("Administration");

                entity.HasKey(a => a.Id);

                entity.Property(a => a.Name)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(a => a.Description)
                    .HasMaxLength(1000);

                entity.HasMany(a => a.Employees)
                    .WithOne(e => e.Administration)
                    .HasForeignKey(e => e.AdministrationId)
                    .OnDelete(DeleteBehavior.NoAction);
            });

            modelBuilder.Entity<Employee>(entity =>
            {
                entity.ToTable("Employee");

                entity.HasKey(a => a.Id);

                entity.Property(a => a.NameAr)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(a => a.NameEng)
                    .HasMaxLength(100);

                entity.HasIndex(e => e.NameAr);

                entity.HasIndex(e => new { e.Phone, e.Email, e.NID })
                    .IsUnique();
            });

            modelBuilder.Entity<Customer>(entity =>
            {
                entity.ToTable("Customer");

                entity.HasKey(c => c.Id);

                entity.Property(c => c.Name)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.Property(c => c.Email)
                    .IsRequired()
                    .HasMaxLength(100);

                entity.HasIndex(c => c.Email)
                    .IsUnique();

                entity.HasMany(c => c.Orders)
                    .WithOne(o => o.Customer)
                    .HasForeignKey(o => o.CustomerId)
                    .OnDelete(DeleteBehavior.Cascade);
            });

            modelBuilder.Entity<Order>(entity =>
            {
                entity.ToTable("Order");

                entity.HasKey(o => o.Id);

                entity.Property(o => o.Date)
                    .IsRequired();

                entity.Property(o => o.Total)
                    .IsRequired();

                entity.Property(o => o.Status)
                    .IsRequired()
                    .HasMaxLength(50);
            });
        }
    }
}
