-- Create Customer table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customer' AND xtype='U')
BEGIN
    CREATE TABLE [Customer] (
        [Id] int IDENTITY(1,1) NOT NULL,
        [Name] nvarchar(100) NOT NULL,
        [Email] nvarchar(100) NOT NULL,
        [Password] nvarchar(255) NOT NULL,
        CONSTRAINT [PK_Customer] PRIMARY KEY ([Id])
    );
    
    CREATE UNIQUE INDEX [IX_Customer_Email] ON [Customer] ([Email]);
END

-- Create Order table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Order' AND xtype='U')
BEGIN
    CREATE TABLE [Order] (
        [Id] int IDENTITY(1,1) NOT NULL,
        [Date] datetime2 NOT NULL,
        [Total] real NOT NULL,
        [Status] nvarchar(50) NOT NULL,
        [CustomerId] int NOT NULL,
        CONSTRAINT [PK_Order] PRIMARY KEY ([Id]),
        CONSTRAINT [FK_Order_Customer_CustomerId] FOREIGN KEY ([CustomerId]) REFERENCES [Customer] ([Id]) ON DELETE CASCADE
    );
    
    CREATE INDEX [IX_Order_CustomerId] ON [Order] ([CustomerId]);
END
