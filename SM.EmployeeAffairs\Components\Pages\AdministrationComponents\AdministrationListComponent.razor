﻿@page "/AdministrationList"
@inject NavigationManager NavigationManager
@inject HttpClient Http
@inject ISnackbar snackbar
@inject IDialogService DialogService

@rendermode InteractiveServer
@using SM.EmployeeAffairs.Components.Pages.Dialogs
@using SM.EmployeeAffairs.Data.Entities

<MudTable Items="Administrations"
          Hover="true"
          Loading="@_loading"
          LoadingProgressColor="Color.Info"
          Dense="true">
    <ToolBarContent>
        <MudSpacer />
        <MudButton Color="Color.Primary"
                   Variant="Variant.Filled"
                   StartIcon="@Icons.Material.Filled.Add"
                   @onclick="NavigateToAddAdministration">إضافة</MudButton>
    </ToolBarContent>
    <HeaderContent>
        <MudTh>رقم معرف</MudTh>
        <MudTh>الإسم</MudTh>
        <MudTh>الوصف</MudTh>
        <MudTh>العمليات</MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd DataLabel="رقم معرف">@context.Id</MudTd>
        <MudTd DataLabel="الاسم">@context.Name</MudTd>
        <MudTd DataLabel="الوصف">@context.Description</MudTd>
        <MudTd>
            <MudTooltip Text="تعديل">
                <MudIconButton Color="Color.Primary"
                               Icon="@Icons.Material.Filled.Edit"
                               OnClick="()=>NavigateToAddAdministration(context)" />
            </MudTooltip>
            <MudTooltip Text="حذف">
                <MudIconButton Color="Color.Error"
                               Icon="@Icons.Material.Filled.Delete"
                               OnClick="()=> DeleteAdministration(context)" />
            </MudTooltip>
        </MudTd>
    </RowTemplate>
</MudTable>

@code {
    private bool _loading = false;
    public List<Administration> Administrations { get; set; } = new List<Administration>();

    protected override async Task OnInitializedAsync()
    {
        _loading = true;
        Administrations = await Http.GetFromJsonAsync<List<Administration>>("/api/administrations");
        _loading = false;
    }

    public void NavigateToAddAdministration()
    {
        NavigationManager.NavigateTo("/AdministrationEdit");
    }

    public void NavigateToAddAdministration(Administration administration)
    {
        NavigationManager.NavigateTo($"/AdministrationEdit/{administration.Id}");
    }

    public async Task DeleteAdministration(Administration administration)
    {

        var parameters = new DialogParameters
        {
            { "ContentText", "هل أنت متأكد من عملية الحذف؟" },
            { "ConfirmationText", "حذف" },
            { "CancelText", "إلغاء" },
            { "Color", Color.Error }
        };

        var _dialogOptions = new DialogOptions() { MaxWidth = MaxWidth.Medium, BackdropClick = false };

        var msg = $"سيتم حذف الإدارة: {administration.Name}";

        var dialog = await DialogService.ShowAsync<ConfirmDelete>(msg, parameters, _dialogOptions);

        var result = await dialog.Result;

        if (result != null && !result.Canceled)
        {
            await Http.DeleteAsync($"/api/administrations/{administration.Id}");
            Administrations.Remove(administration);
            snackbar.Configuration.PositionClass = Defaults.Classes.Position.TopCenter;
            snackbar.Add("تم الحذف بنجاح", Severity.Success);
        }
    }
}
