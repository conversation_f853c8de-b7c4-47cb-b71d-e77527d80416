﻿@page "/Account/Manage/PersonalData"

@inject IdentityUserAccessor UserAccessor

<PageTitle>Personal Data</PageTitle>

<MudText Typo="Typo.h6" GutterBottom="true">Personal data</MudText>

<StatusMessage />

<MudGrid>
    <MudItem md="12">
        <MudText Typo="Typo.body1">
            Your account contains personal data that you have given us. This page allows you to download or delete that data.
        </MudText>
    </MudItem>
    <MudItem md="12">
        <MudAlert Severity="Severity.Warning" Variant="Variant.Text">
            Deleting this data will permanently remove your account, and this cannot be recovered.
        </MudAlert>
    </MudItem>
    <MudItem md="12">
        <form action="Account/Manage/DownloadPersonalData" method="post">
            <AntiforgeryToken />
            <MudStaticButton Variant="Variant.Filled" Color="Color.Primary" FullWidth="true" FormAction="FormAction.Submit">Download</MudStaticButton>
        </form>
    </MudItem>
    <MudItem md="12">
        <MudLink Href="Account/Manage/DeletePersonalData" Color="Color.Error">Delete</MudLink>
    </MudItem>
</MudGrid>

@code {
    [CascadingParameter]
    private HttpContext HttpContext { get; set; } = default!;

    protected override async Task OnInitializedAsync()
    {
        _ = await UserAccessor.GetRequiredUserAsync(HttpContext);
    }
}
