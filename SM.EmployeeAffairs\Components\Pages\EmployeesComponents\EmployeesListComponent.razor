@using SM.EmployeeAffairs.Components.Pages.Dialogs
@using SM.EmployeeAffairs.Data.Entities
@inject HttpClient Http
@inject ISnackbar Snackbar
@inject IDialogService DialogService
@rendermode InteractiveServer

<p> @AdministrationId</p>

<MudTable Items="Employees"
          Hover="true"
          Loading="loading"
          LoadingProgressColor="Color.Info"
          Dense="true">
    <ToolBarContent>
        <MudSpacer />
        <MudButton Color="Color.Primary"
                   Variant="Variant.Filled"
                   StartIcon="@Icons.Material.Filled.Add"
                   @onclick="OpenAddEmployeeDialog">????? ????</MudButton>
    </ToolBarContent>
    <HeaderContent>
        <MudTh>????? ???????</MudTh>
        <MudTh>????? ??????</MudTh>
        <MudTh>????? (????)</MudTh>
        <MudTh>????? (???????)</MudTh>
        <MudTh>??????</MudTh>
        <MudTh>?????? ??????????</MudTh>
        <MudTh>????????</MudTh>
    </HeaderContent>
    <RowTemplate>
        <MudTd DataLabel="????? ???????">@context.EmployementNumber</MudTd>
        <MudTd DataLabel="????? ??????">@context.NID</MudTd>
        <MudTd DataLabel="????? (????)">@context.NameAr</MudTd>
        <MudTd DataLabel="????? (???????)">@context.NameEng</MudTd>
        <MudTd DataLabel="??????">@context.Phone</MudTd>
        <MudTd DataLabel="?????? ??????????">@context.Email</MudTd>
        <MudTd>
            <MudTooltip Text="?????">
                <MudIconButton Color="Color.Primary" Icon="@Icons.Material.Filled.Edit" OnClick="() => OpenEditDialog(context)" />
            </MudTooltip>
            <MudTooltip Text="???">
                <MudIconButton Color="Color.Error" Icon="@Icons.Material.Filled.Delete" OnClick="() => DeleteEmployee(context.Id)" />
            </MudTooltip>
        </MudTd>
    </RowTemplate>
</MudTable>



@code {
    [CascadingParameter(Name = "AdministrationId")] public Guid AdministrationId { get; set; }


    public List<Employee> Employees = new();
    private bool loading = false;
    private Employee selectedEmployee = new();

    protected override async Task OnParametersSetAsync()
    {
        await LoadEmployees();

    }

    private void OpenAddEmployeeDialog()
    {
        selectedEmployee = new Employee { AdministrationId = AdministrationId };

        var parameteres = new DialogParameters
        {
            ["EmployeeId"] = null,
            ["AdministrationId"] = AdministrationId,
            ["OnSave"] = EventCallback.Factory.Create(this, OnDialogSave)
        };

        DialogService.ShowAsync<EmployeeEditComponent>(
            "????? ????",
            parameteres,
            new DialogOptions { MaxWidth = MaxWidth.Medium }
        );

    }

    private void OpenEditDialog(Employee employee)
    {
        selectedEmployee = employee;
        var parameters = new DialogParameters
        {
            ["EmployeeId"] = employee.Id,
            ["AdministrationId"] = employee.AdministrationId,
            ["OnSave"] = EventCallback.Factory.Create(this, OnDialogSave)
        };

        DialogService.ShowAsync<EmployeeEditComponent>("????? ?????? ????",
        parameters,
        new DialogOptions { MaxWidth = MaxWidth.Small });
    }
    private async Task LoadEmployees()
    {
        loading = true;
        Employees = await Http.GetFromJsonAsync<List<Employee>>($"/api/employees/by-administration/{AdministrationId}") ?? new();
        loading = false;
    }


    private async Task OnDialogSave()
    {
        await LoadEmployees();
    }
    private async Task DeleteEmployee(Guid employeeId)
    {
        var parameters = new DialogParameters
        {
            { "ContentText", "?? ??? ????? ?? ????? ????? ?" },
            { "ConfirmationText", "???" },
            { "CancelText", "?????" },
            { "Color", Color.Error }
        };

        var _dialogOptions = new DialogOptions() { MaxWidth = MaxWidth.Medium, BackdropClick = false };
        var msg = $"???? ??? ??????: {selectedEmployee.NameAr}";
        var dialog = await DialogService.ShowAsync<ConfirmDelete>(msg, parameters, _dialogOptions);
        var result = await dialog.Result;

        if (result != null && !result.Canceled)
        {
            var response = await Http.DeleteAsync($"/api/employees/{employeeId}");
            if (response.IsSuccessStatusCode)
            {
                Employees = Employees.Where(e => e.Id != employeeId).ToList();
                Snackbar.Add("?? ??? ?????? ?????", Severity.Success);
            }
            else
            {
                Snackbar.Add("??? ??? ??????", Severity.Error);
            }
        }
    }
}
